# 文本切分优化和图片处理功能指南

## 概述

本次优化主要解决了两个核心问题：
1. **文本块过细问题**：通过调整切分参数，增大文本块大小，提高检索质量
2. **图片处理缺失**：新增图片提取、MinIO存储和召回功能，支持多媒体内容检索

## 优化内容详解

### 1. 文本切分参数优化

#### 原始配置
```python
chunk_size: int = 1024      # 固定chunk大小
chunk_overlap: int = 256    # chunk重叠大小  
min_chunk_size: int = 100   # 最小chunk大小
semantic_threshold: float = 0.82  # 语义相似度阈值
```

#### 优化后配置
```python
chunk_size: int = 2048      # 增大到2048，减少过细切分
chunk_overlap: int = 512    # 相应增大重叠，保持上下文连续性
min_chunk_size: int = 200   # 提高最小大小，过滤过小片段
semantic_threshold: float = 0.75  # 降低阈值，增加语义块大小
max_chunk_size: int = 4096  # 新增最大限制，防止块过大
preserve_structure: bool = True  # 新增结构保持选项
```

#### 优化效果
- **文本块数量减少**：相同文档的文本块数量减少约30-50%
- **检索质量提升**：更大的文本块包含更完整的语义信息
- **上下文连续性**：增大的重叠确保重要信息不被截断
- **过滤噪声**：提高最小块大小，减少无意义的小片段

### 2. 图片处理功能

#### 新增组件

##### ImageProcessor类 (`utils/image_processor.py`)
- **支持格式**：PDF、Word (docx)、PowerPoint (pptx)
- **图片提取**：自动从文档中提取所有图片
- **MinIO存储**：将图片保存到MinIO对象存储
- **元数据管理**：记录图片位置、页码、替代文本等信息

##### ImageInfo数据类
```python
@dataclass
class ImageInfo:
    image_id: str           # 图片唯一ID
    filename: str           # 原始文件名
    minio_url: str         # MinIO存储URL
    page_number: Optional[int]     # 页码（PDF/PPT）
    slide_number: Optional[int]    # 幻灯片编号（PPT）
    position: Optional[str]        # 图片位置描述
    alt_text: Optional[str]        # 图片替代文本
    caption: Optional[str]         # 图片标题
    size: Optional[Tuple[int, int]] # 图片尺寸
    format: Optional[str]          # 图片格式
```

#### 图片处理流程

1. **文档上传** → 2. **文本提取** → 3. **图片提取** → 4. **MinIO存储** → 5. **生成链接** → 6. **嵌入文本块** → 7. **存储到Milvus**

#### 图片引用格式
```
[图片: uuid-123] (第1页) - 深能集团组织架构图 [链接: http://minio-url/image.png]
```

### 3. 数据模型扩展

#### Milvus Schema新增字段
```python
# 图片相关字段
schema.add_field("image_urls", DataType.VARCHAR, max_length=65535)  # 图片URL列表（JSON）
schema.add_field("has_images", DataType.BOOL)  # 是否包含图片标识
```

#### 存储格式
```json
{
  "image_urls": "[{\"url\":\"http://minio/img1.png\",\"id\":\"uuid1\",\"page\":1}]",
  "has_images": true
}
```

### 4. API接口增强

#### 插入接口 (`/insert/file`)
- **新增功能**：自动提取和处理图片
- **返回信息**：包含提取的图片数量和链接信息
- **支持格式**：PDF、Word、PPT、Markdown等

#### 查询接口 (`/query/`)
- **新增字段**：返回结果包含`images`数组
- **图片信息**：每个图片包含URL、来源、位置等元数据
- **关联性**：图片与对应的文本块关联

### 5. 使用示例

#### 文件上传示例
```python
import requests

# 上传包含图片的PDF文件
with open('report.pdf', 'rb') as f:
    files = {'file': ('report.pdf', f, 'application/pdf')}
    data = {
        'rag_id': 'company_reports',
        'split_strategy': 'hybrid',
        'update_mode': 'incremental'
    }
    
    response = requests.post(
        'http://localhost:3008/insert/file',
        files=files,
        data=data
    )
    
    result = response.json()
    print(f"插入了 {result['chunks_count']} 个文本块")
    print(f"提取了 {result['details']['images_count']} 张图片")
```

#### 查询示例
```python
query_data = {
    "query": "公司组织架构是怎样的？",
    "rag_id": ["company_reports"]
}

response = requests.post(
    'http://localhost:3008/query/',
    json=query_data
)

result = response.json()
print(f"回答: {result['answer']}")
print(f"相关图片: {len(result['images'])} 张")

for img in result['images']:
    print(f"- {img['filename']}: {img['url']}")
```

## 配置建议

### 1. 文本切分策略选择

- **fixed**: 适用于结构化文档，切分速度快
- **semantic**: 适用于长篇文章，保持语义完整性
- **hybrid**: 推荐使用，结合语义和固定大小的优势

### 2. MinIO配置

确保MinIO服务正常运行，并在`config.py`中正确配置：
```python
MINIO_ENDPOINT = "*************:9000"
MINIO_ACCESS_KEY = "minioadmin"
MINIO_SECRET_KEY = "minioadmin"
bucket_name = "milvus-bucket"
```

### 3. 依赖包安装

```bash
# 图片处理相关
pip install Pillow PyMuPDF python-docx python-pptx

# MinIO客户端
pip install minio

# 其他依赖
pip install fastapi uvicorn pymilvus sentence-transformers
```

## 性能优化建议

### 1. 文本切分性能
- 对于大文档，建议使用`hybrid`策略
- 语义切分会消耗更多计算资源，可根据需要调整
- 可以通过调整`semantic_threshold`来平衡质量和性能

### 2. 图片处理性能
- 图片提取是IO密集型操作，建议异步处理
- 大文件建议分批处理，避免内存溢出
- 可以设置图片大小限制，过滤过小的图片

### 3. 存储优化
- MinIO建议配置多副本，确保数据安全
- 可以设置图片过期策略，定期清理无用图片
- 建议对图片进行压缩，减少存储空间

## 故障排除

### 1. 图片提取失败
- 检查依赖包是否正确安装
- 确认文件格式是否支持
- 查看日志中的具体错误信息

### 2. MinIO连接失败
- 检查MinIO服务是否运行
- 确认网络连接和端口配置
- 验证访问密钥是否正确

### 3. 文本切分异常
- 检查文本编码是否正确
- 确认语义模型是否加载成功
- 调整切分参数，避免过大或过小的块

## 监控和维护

### 1. 关键指标
- 文本块平均大小
- 图片提取成功率
- MinIO存储使用量
- 查询响应时间

### 2. 日志监控
- 关注图片处理错误日志
- 监控MinIO上传失败
- 跟踪文本切分性能

### 3. 定期维护
- 清理过期的临时文件
- 检查MinIO存储空间
- 更新依赖包版本

## 总结

本次优化显著提升了RAG系统的功能和性能：

1. **文本质量提升**：更大的文本块包含更完整的语义信息
2. **多媒体支持**：支持图片提取和展示，丰富了检索结果
3. **用户体验改善**：前端可以展示相关图片，提供更直观的信息
4. **系统扩展性**：为后续支持更多媒体类型奠定了基础

通过这些优化，RAG系统能够更好地处理包含图片的文档，为用户提供更丰富、更准确的检索结果。
