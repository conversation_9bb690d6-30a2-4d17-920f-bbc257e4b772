<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片链接解析示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .answer-content {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .embedded-image {
            display: inline-block;
            margin: 10px 0;
            padding: 8px;
            background: #e3f2fd;
            border-radius: 4px;
            border: 1px solid #2196f3;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .embedded-image:hover {
            background: #bbdefb;
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }
        
        .image-description {
            color: #1976d2;
            font-weight: bold;
            text-decoration: none;
        }
        
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            position: relative;
            margin: 5% auto;
            max-width: 90%;
            max-height: 90%;
        }
        
        .modal-image {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        
        .close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            opacity: 0.7;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .original-text {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .parsed-result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>图片链接嵌入解析示例</h1>
    
    <div class="test-section">
        <h2 class="test-title">功能说明</h2>
        <p>本示例展示如何解析LLM回答中嵌入的图片链接，格式为：<code>描述:img[链接]</code></p>
        <p>点击图片链接可以预览图片，支持模态框展示。</p>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">测试示例 1：组织架构查询</h2>
        <div class="container">
            <h3>原始LLM回答：</h3>
            <div class="original-text">
深能集团采用现代化的管理结构，具体组织架构如下：

深能集团组织架构图:img[http://*************:9000/milvus-bucket/test_doc/org_chart_001.png]

管理层包括董事会、总经理和各部门总监。业务部门主要分为三大事业部：

业务部门结构图:img[http://*************:9000/milvus-bucket/test_doc/dept_structure_002.png]

1. 风电事业部 - 负责风力发电项目
2. 光伏事业部 - 专注太阳能发电
3. 水电事业部 - 参与水电站建设
            </div>
            
            <h3>解析后的展示效果：</h3>
            <div class="answer-content" id="answer1">
                <!-- 这里将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">测试示例 2：技术报告查询</h2>
        <div class="container">
            <h3>原始LLM回答：</h3>
            <div class="original-text">
深能集团在清洁能源技术方面取得了显著进展：

技术创新成果图(第3页):img[http://*************:9000/milvus-bucket/test_doc/tech_innovation_003.png]

主要技术突破包括：
1. 高效风力发电技术
2. 先进光伏发电系统
3. 智能电网管理

设备展示图(第4页):img[http://*************:9000/milvus-bucket/test_doc/equipment_004.png]

这些技术创新为公司的可持续发展提供了强有力的支撑。
            </div>
            
            <h3>解析后的展示效果：</h3>
            <div class="answer-content" id="answer2">
                <!-- 这里将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    
    <!-- 图片模态框 -->
    <div id="imageModal" class="image-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
        </div>
    </div>
    
    <script>
        // 解析图片链接的正则表达式
        const imagePattern = /([^:]+):img\[([^\]]+)\]/g;
        
        /**
         * 解析包含图片链接的文本
         * @param {string} text - 包含图片链接的原始文本
         * @returns {string} - 解析后的HTML
         */
        function parseImageLinks(text) {
            return text.replace(imagePattern, (match, description, url) => {
                const cleanDescription = description.trim();
                const cleanUrl = url.trim();
                
                return `<span class="embedded-image" onclick="showImage('${cleanUrl}', '${cleanDescription}')">
                    <span class="image-description">📷 ${cleanDescription}</span>
                </span>`;
            });
        }
        
        /**
         * 显示图片模态框
         * @param {string} url - 图片URL
         * @param {string} description - 图片描述
         */
        function showImage(url, description) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            modalImage.src = url;
            modalImage.alt = description;
            modal.style.display = 'block';
        }
        
        /**
         * 关闭图片模态框
         */
        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
        }
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 测试示例1
            const answer1Text = `深能集团采用现代化的管理结构，具体组织架构如下：

深能集团组织架构图:img[http://*************:9000/milvus-bucket/test_doc/org_chart_001.png]

管理层包括董事会、总经理和各部门总监。业务部门主要分为三大事业部：

业务部门结构图:img[http://*************:9000/milvus-bucket/test_doc/dept_structure_002.png]

1. 风电事业部 - 负责风力发电项目
2. 光伏事业部 - 专注太阳能发电  
3. 水电事业部 - 参与水电站建设`;
            
            // 测试示例2
            const answer2Text = `深能集团在清洁能源技术方面取得了显著进展：

技术创新成果图(第3页):img[http://*************:9000/milvus-bucket/test_doc/tech_innovation_003.png]

主要技术突破包括：
1. 高效风力发电技术
2. 先进光伏发电系统
3. 智能电网管理

设备展示图(第4页):img[http://*************:9000/milvus-bucket/test_doc/equipment_004.png]

这些技术创新为公司的可持续发展提供了强有力的支撑。`;
            
            // 解析并显示
            document.getElementById('answer1').innerHTML = parseImageLinks(answer1Text).replace(/\n/g, '<br>');
            document.getElementById('answer2').innerHTML = parseImageLinks(answer2Text).replace(/\n/g, '<br>');
            
            // 绑定模态框关闭事件
            document.querySelector('.close').onclick = closeModal;
            document.getElementById('imageModal').onclick = function(e) {
                if (e.target === this) {
                    closeModal();
                }
            };
            
            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        });
        
        // 提供给外部调用的API
        window.RAGImageParser = {
            parse: parseImageLinks,
            showImage: showImage,
            closeModal: closeModal
        };
        
        // 使用示例
        console.log('RAG图片解析器已加载');
        console.log('使用方法：');
        console.log('1. RAGImageParser.parse(text) - 解析包含图片链接的文本');
        console.log('2. RAGImageParser.showImage(url, description) - 显示图片');
        console.log('3. RAGImageParser.closeModal() - 关闭图片模态框');
    </script>
</body>
</html>
