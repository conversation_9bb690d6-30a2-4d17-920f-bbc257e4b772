"""
全量更新/增量更新
文件上传、文本分块保存等操作
"""
try:
    # 尝试相对导入（当作为模块运行时）
    from .milvus import MilvusVanna
    from ..utils.text_splitter import TextSplitter, SplitConfig
    from .delete import MilvusDelete
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import MilvusVanna
    from utils.text_splitter import TextSplitter, SplitConfig
    from operation.delete import MilvusDelete

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel
from typing import List, Optional
import logging
import hashlib
import uuid
import os
import tempfile
from datetime import datetime
try:
    import docx
except ImportError:
    docx = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from pptx import Presentation
except ImportError:
    Presentation = None


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/insert", tags=["插入数据"])


class InsertRequest(BaseModel):
    """插入请求模型"""
    rag_id: Optional[str] = None  # 知识库ID，如果不提供则自动生成
    doc_id: Optional[str] = None  # 文档ID，如果不提供则自动生成
    source: Optional[str] = None  # 文件来源描述
    content: Optional[str] = None  # 直接提供的文本内容
    split_strategy: str = "hybrid"  # 文本分块策略：fixed, semantic, hybrid
    update_mode: str = "incremental"  # 更新模式：full(全量), incremental(增量)


class BatchInsertRequest(BaseModel):
    """批量插入请求模型"""
    rag_id: Optional[str] = None  # 知识库ID，如果不提供则自动生成
    split_strategy: str = "hybrid"  # 文本分块策略：fixed, semantic, hybrid
    update_mode: str = "incremental"  # 更新模式：full(全量), incremental(增量)
    auto_generate_rag_id: bool = True  # 是否自动生成rag_id


class BatchInsertResponse(BaseModel):
    """批量插入响应模型"""
    success: bool
    message: str
    rag_id: str
    total_files: int
    successful_files: int
    failed_files: int
    file_results: List[dict]
    details: dict


class InsertResponse(BaseModel):
    """插入响应模型"""
    success: bool
    message: str
    doc_id: str
    chunks_count: int
    details: dict


class MilvusInsert(MilvusVanna):
    def __init__(self, config=None):
        super().__init__(config)
        self.text_splitter = TextSplitter()
        self.milvus_delete = MilvusDelete(config)

    def _generate_rag_id(self, prefix: str = "rag") -> str:
        """
        生成唯一的知识库ID

        Args:
            prefix (str): ID前缀

        Returns:
            str: 生成的知识库ID
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}_{timestamp}_{unique_id}"

    def _extract_text_from_file(self, file_path: str, file_type: str) -> str:
        """
        从文件中提取文本内容

        Args:
            file_path (str): 文件路径
            file_type (str): 文件类型

        Returns:
            str: 提取的文本内容
        """
        try:
            if file_type.lower() == 'txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()

            elif file_type.lower() == 'pdf':
                if PyPDF2 is None:
                    raise ValueError("PyPDF2 未安装，无法处理PDF文件")
                text = ""
                with open(file_path, 'rb') as f:
                    pdf_reader = PyPDF2.PdfReader(f)
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
                return text

            elif file_type.lower() in ['doc', 'docx']:
                if docx is None:
                    raise ValueError("python-docx 未安装，无法处理Word文件")
                doc = docx.Document(file_path)
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text

            elif file_type.lower() in ['xls', 'xlsx']:
                if pd is None:
                    raise ValueError("pandas 未安装，无法处理Excel文件")
                df = pd.read_excel(file_path)
                return df.to_string()

            elif file_type.lower() == 'csv':
                if pd is None:
                    raise ValueError("pandas 未安装，无法处理CSV文件")
                df = pd.read_csv(file_path)
                return df.to_string()

            elif file_type.lower() in ['ppt', 'pptx']:
                if Presentation is None:
                    raise ValueError("python-pptx 未安装，无法处理PowerPoint文件")
                return self._extract_text_from_ppt(file_path)

            elif file_type.lower() in ['md', 'markdown']:
                return self._extract_text_from_markdown(file_path)

            else:
                raise ValueError(f"不支持的文件类型: {file_type}")

        except Exception as e:
            logger.error(f"文件内容提取失败: {str(e)}")
            raise Exception(f"Failed to extract text from file: {str(e)}")

    def _extract_text_from_ppt(self, file_path: str) -> str:
        """
        从PowerPoint文件中提取文本内容

        Args:
            file_path (str): PPT文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            prs = Presentation(file_path)
            text_content = []

            for slide_num, slide in enumerate(prs.slides, 1):
                slide_text = []
                slide_text.append(f"\n=== 幻灯片 {slide_num} ===\n")

                # 提取幻灯片中的所有文本
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                    # 处理表格中的文本
                    if shape.has_table:
                        table_text = []
                        for row in shape.table.rows:
                            row_text = []
                            for cell in row.cells:
                                if cell.text.strip():
                                    row_text.append(cell.text.strip())
                            if row_text:
                                table_text.append(" | ".join(row_text))
                        if table_text:
                            slide_text.append("表格内容:")
                            slide_text.extend(table_text)

                    # 处理图表中的文本（如果有）
                    if shape.has_chart:
                        try:
                            chart = shape.chart
                            if hasattr(chart, 'chart_title') and chart.chart_title:
                                slide_text.append(f"图表标题: {chart.chart_title.text_frame.text}")
                        except:
                            pass  # 忽略图表处理错误

                # 添加幻灯片内容
                if len(slide_text) > 1:  # 除了标题外还有内容
                    text_content.extend(slide_text)
                    text_content.append("")  # 添加空行分隔

            return "\n".join(text_content)

        except Exception as e:
            logger.error(f"PPT文件内容提取失败: {str(e)}")
            raise Exception(f"Failed to extract text from PPT file: {str(e)}")

    def _extract_text_from_markdown(self, file_path: str) -> str:
        """
        从Markdown文件中提取文本内容

        Args:
            file_path (str): Markdown文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 可以选择保留Markdown格式或转换为纯文本
            # 这里我们保留Markdown格式，因为它本身就是文本
            # 如果需要转换为纯文本，可以使用markdown库

            # 简单的格式化处理
            processed_content = self._process_markdown_content(content)

            return processed_content

        except Exception as e:
            logger.error(f"Markdown文件内容提取失败: {str(e)}")
            raise Exception(f"Failed to extract text from Markdown file: {str(e)}")

    def _process_markdown_content(self, content: str) -> str:
        """
        处理Markdown内容，提取和格式化文本

        Args:
            content (str): 原始Markdown内容

        Returns:
            str: 处理后的文本内容
        """
        try:
            import re

            # 移除HTML标签（如果有）
            content = re.sub(r'<[^>]+>', '', content)

            # 处理标题 - 保留标题结构但简化格式
            content = re.sub(r'^#{1,6}\s+(.+)$', r'\1', content, flags=re.MULTILINE)

            # 处理代码块 - 保留代码内容但移除标记
            content = re.sub(r'```[\w]*\n(.*?)\n```', r'\1', content, flags=re.DOTALL)
            content = re.sub(r'`([^`]+)`', r'\1', content)

            # 处理链接 - 保留链接文本
            content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)

            # 处理图片 - 保留alt文本
            content = re.sub(r'!\[([^\]]*)\]\([^\)]+\)', r'图片: \1', content)

            # 处理粗体和斜体 - 保留文本内容
            content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
            content = re.sub(r'\*([^*]+)\*', r'\1', content)
            content = re.sub(r'__([^_]+)__', r'\1', content)
            content = re.sub(r'_([^_]+)_', r'\1', content)

            # 处理列表 - 简化格式
            content = re.sub(r'^\s*[-*+]\s+', '• ', content, flags=re.MULTILINE)
            content = re.sub(r'^\s*\d+\.\s+', '• ', content, flags=re.MULTILINE)

            # 处理引用
            content = re.sub(r'^>\s+', '', content, flags=re.MULTILINE)

            # 处理表格 - 保留表格内容但简化格式
            lines = content.split('\n')
            processed_lines = []
            in_table = False

            for line in lines:
                # 检测表格行
                if '|' in line and line.strip():
                    # 简单的表格行处理
                    if not re.match(r'^\s*\|[\s\-\|:]+\|\s*$', line):  # 跳过分隔行
                        table_cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                        if table_cells:
                            processed_lines.append(' | '.join(table_cells))
                            in_table = True
                    continue
                else:
                    if in_table:
                        processed_lines.append('')  # 表格后添加空行
                        in_table = False
                    processed_lines.append(line)

            content = '\n'.join(processed_lines)

            # 清理多余的空行
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

            return content.strip()

        except Exception as e:
            logger.error(f"Markdown内容处理失败: {str(e)}")
            # 如果处理失败，返回原始内容
            return content

    def _generate_content_hash(self, content: str) -> str:
        """生成内容的MD5哈希值"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _check_content_exists(self, collection_name: str, content_hash: str) -> bool:
        """检查内容是否已存在"""
        client = self._get_or_create_collection(collection_name)
        try:
            results = client.query(
                collection_name=collection_name,
                filter=f"content_hash == '{content_hash}'",
                output_fields=["id"]
            )
            return len(results) > 0
        except Exception as e:
            logger.error(f"检查内容是否存在失败: {str(e)}")
            return False
        finally:
            client.close()

    def _insert_chunks(self, collection_name: str, chunks: List[str],
                      rag_id: str, doc_id: str, source: str) -> int:
        """
        将文本块插入到Milvus中

        Args:
            collection_name (str): 集合名称
            chunks (List[str]): 文本块列表
            rag_id (str): 知识库ID
            doc_id (str): 文档ID
            source (str): 文件来源

        Returns:
            int: 插入的记录数量
        """
        client = self._get_or_create_collection(collection_name)

        try:
            data = []
            for i, chunk in enumerate(chunks):
                chunk_id = f"{doc_id}_chunk_{i}"
                record_id = f"{rag_id}_{doc_id}_{chunk_id}"
                content_hash = self._generate_content_hash(chunk)

                # 生成dense embedding
                dense_embedding = self.embedding_model.encode([chunk])[0].tolist()

                data.append({
                    "id": record_id,
                    "content": chunk,
                    "rag_id": rag_id,
                    "source": source,
                    "doc_id": doc_id,
                    "chunk_id": chunk_id,
                    "dense": dense_embedding,
                    "content_hash": content_hash
                })

            # 批量插入数据
            result = client.insert(collection_name=collection_name, data=data)
            return len(data)

        except Exception as e:
            logger.error(f"插入数据失败: {str(e)}")
            raise Exception(f"Failed to insert chunks: {str(e)}")
        finally:
            client.close()

    def insert_file_content(self, collection_name: str, request: InsertRequest,
                           file_content: str = None) -> InsertResponse:
        """
        插入文件内容到知识库

        Args:
            collection_name (str): 集合名称
            request (InsertRequest): 插入请求
            file_content (str): 文件内容（如果是文件上传）

        Returns:
            InsertResponse: 插入结果
        """
        try:
            # 生成知识库ID（如果未提供）
            rag_id = request.rag_id or self._generate_rag_id()

            # 生成文档ID
            doc_id = request.doc_id or str(uuid.uuid4())

            # 获取文本内容
            if file_content:
                content = file_content
            elif request.content:
                content = request.content
            else:
                raise ValueError("必须提供文件内容或文本内容")

            if not content.strip():
                raise ValueError("文本内容不能为空")

            # 全量更新模式：先删除该文档的所有记录
            if request.update_mode == "full":
                try:
                    deleted_count = self.milvus_delete.delete_by_doc_ids(collection_name, [doc_id])
                    logger.info(f"全量更新模式：删除了 {deleted_count} 条旧记录")
                except Exception as e:
                    logger.warning(f"删除旧记录失败，继续插入新记录: {str(e)}")

            # 增量更新模式：检查内容是否已存在
            elif request.update_mode == "incremental":
                content_hash = self._generate_content_hash(content)
                if self._check_content_exists(collection_name, content_hash):
                    return InsertResponse(
                        success=True,
                        message="内容已存在，跳过插入",
                        doc_id=doc_id,
                        chunks_count=0,
                        details={"status": "skipped", "reason": "content_exists"}
                    )

            # 文本分块
            chunks = self.text_splitter.split(content, strategy=request.split_strategy)

            if not chunks:
                raise ValueError("文本分块失败，没有生成有效的文本块")

            # 插入文本块
            source = request.source or f"uploaded_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            inserted_count = self._insert_chunks(collection_name, chunks, rag_id, doc_id, source)

            return InsertResponse(
                success=True,
                message=f"成功插入 {inserted_count} 个文本块",
                doc_id=doc_id,
                chunks_count=inserted_count,
                details={
                    "rag_id": rag_id,
                    "split_strategy": request.split_strategy,
                    "update_mode": request.update_mode,
                    "source": source
                }
            )

        except Exception as e:
            logger.error(f"插入文件内容失败: {str(e)}")
            return InsertResponse(
                success=False,
                message=f"插入失败: {str(e)}",
                doc_id=request.doc_id or "",
                chunks_count=0,
                details={"error": str(e)}
            )

    def batch_insert_contents(self, collection_name: str, contents: List[dict]) -> dict:
        """
        批量插入多个内容

        Args:
            collection_name (str): 集合名称
            contents (List[dict]): 内容列表，每个元素包含 rag_id, content, source 等信息

        Returns:
            dict: 批量插入结果
        """
        results = {
            "total": len(contents),
            "success": 0,
            "failed": 0,
            "details": []
        }

        for i, content_info in enumerate(contents):
            try:
                request = InsertRequest(
                    rag_id=content_info.get("rag_id"),
                    doc_id=content_info.get("doc_id"),
                    source=content_info.get("source"),
                    content=content_info.get("content"),
                    split_strategy=content_info.get("split_strategy", "hybrid"),
                    update_mode=content_info.get("update_mode", "incremental")
                )

                result = self.insert_file_content(collection_name, request)

                if result.success:
                    results["success"] += 1
                else:
                    results["failed"] += 1

                results["details"].append({
                    "index": i,
                    "doc_id": result.doc_id,
                    "success": result.success,
                    "message": result.message,
                    "chunks_count": result.chunks_count
                })

            except Exception as e:
                results["failed"] += 1
                results["details"].append({
                    "index": i,
                    "success": False,
                    "message": f"处理失败: {str(e)}",
                    "chunks_count": 0
                })

        return results

    def batch_insert_files(self, collection_name: str, files_data: List[dict],
                          batch_request: BatchInsertRequest) -> BatchInsertResponse:
        """
        批量插入多个文件

        Args:
            collection_name (str): 集合名称
            files_data (List[dict]): 文件数据列表，每个元素包含 filename, content, file_type
            batch_request (BatchInsertRequest): 批量插入请求

        Returns:
            BatchInsertResponse: 批量插入结果
        """
        try:
            # 生成或使用提供的rag_id
            rag_id = batch_request.rag_id
            if not rag_id and batch_request.auto_generate_rag_id:
                rag_id = self._generate_rag_id()
            elif not rag_id:
                raise ValueError("必须提供rag_id或启用自动生成")

            results = {
                "total_files": len(files_data),
                "successful_files": 0,
                "failed_files": 0,
                "file_results": []
            }

            for i, file_data in enumerate(files_data):
                try:
                    # 为每个文件创建插入请求
                    file_request = InsertRequest(
                        rag_id=rag_id,
                        doc_id=None,  # 让系统自动生成
                        source=file_data.get("filename", f"file_{i}"),
                        content=file_data.get("content"),
                        split_strategy=batch_request.split_strategy,
                        update_mode=batch_request.update_mode
                    )

                    # 插入文件内容
                    result = self.insert_file_content(collection_name, file_request)

                    if result.success:
                        results["successful_files"] += 1
                    else:
                        results["failed_files"] += 1

                    results["file_results"].append({
                        "filename": file_data.get("filename", f"file_{i}"),
                        "doc_id": result.doc_id,
                        "success": result.success,
                        "message": result.message,
                        "chunks_count": result.chunks_count
                    })

                except Exception as e:
                    results["failed_files"] += 1
                    results["file_results"].append({
                        "filename": file_data.get("filename", f"file_{i}"),
                        "doc_id": "",
                        "success": False,
                        "message": f"处理失败: {str(e)}",
                        "chunks_count": 0
                    })

            return BatchInsertResponse(
                success=results["failed_files"] == 0,
                message=f"批量处理完成：成功 {results['successful_files']} 个，失败 {results['failed_files']} 个",
                rag_id=rag_id,
                total_files=results["total_files"],
                successful_files=results["successful_files"],
                failed_files=results["failed_files"],
                file_results=results["file_results"],
                details={
                    "split_strategy": batch_request.split_strategy,
                    "update_mode": batch_request.update_mode,
                    "auto_generated_rag_id": batch_request.auto_generate_rag_id and not batch_request.rag_id
                }
            )

        except Exception as e:
            logger.error(f"批量插入文件失败: {str(e)}")
            return BatchInsertResponse(
                success=False,
                message=f"批量插入失败: {str(e)}",
                rag_id=batch_request.rag_id or "",
                total_files=len(files_data),
                successful_files=0,
                failed_files=len(files_data),
                file_results=[],
                details={"error": str(e)}
            )


# 创建插入实例
milvus_insert = MilvusInsert()


# API 端点
@router.post("/file", response_model=InsertResponse)
async def insert_file_upload(
    file: UploadFile = File(...),
    rag_id: Optional[str] = Form(None),
    doc_id: Optional[str] = Form(None),
    source: Optional[str] = Form(None),
    split_strategy: str = Form("hybrid"),
    update_mode: str = Form("incremental")
):
    """
    上传文件并插入到知识库

    支持的文件类型：txt, pdf, doc, docx, xls, xlsx, csv, md
    """
    try:
        # 检查文件类型
        file_extension = file.filename.split('.')[-1].lower() if file.filename else ""
        supported_types = ['txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'ppt', 'pptx', 'md', 'markdown']

        if file_extension not in supported_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_extension}。支持的类型: {', '.join(supported_types)}"
            )

        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 提取文件内容
            file_content = milvus_insert._extract_text_from_file(temp_file_path, file_extension)

            # 创建插入请求
            request = InsertRequest(
                rag_id=rag_id,
                doc_id=doc_id,
                source=source or file.filename,
                content=file_content,
                split_strategy=split_strategy,
                update_mode=update_mode
            )

            # 插入内容
            result = milvus_insert.insert_file_content(
                collection_name=milvus_insert.collection_name,
                request=request
            )

            if not result.success:
                raise HTTPException(status_code=500, detail=result.message)

            return result

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传API调用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.post("/files", response_model=BatchInsertResponse)
async def insert_multiple_files(
    files: List[UploadFile] = File(...),
    rag_id: Optional[str] = Form(None),
    split_strategy: str = Form("hybrid"),
    update_mode: str = Form("incremental"),
    auto_generate_rag_id: bool = Form(True)
):
    """
    批量上传多个文件并插入到知识库

    支持的文件类型：txt, pdf, doc, docx, xls, xlsx, csv, ppt, pptx, md
    """
    try:
        if not files:
            raise HTTPException(status_code=400, detail="至少需要上传一个文件")

        # 检查文件数量限制
        if len(files) > 50:  # 限制最多50个文件
            raise HTTPException(status_code=400, detail="一次最多只能上传50个文件")

        supported_types = ['txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'ppt', 'pptx', 'md', 'markdown']
        files_data = []
        temp_files = []

        try:
            # 处理每个文件
            for file in files:
                # 检查文件类型
                file_extension = file.filename.split('.')[-1].lower() if file.filename else ""
                if file_extension not in supported_types:
                    raise HTTPException(
                        status_code=400,
                        detail=f"文件 {file.filename} 的类型 {file_extension} 不受支持。支持的类型: {', '.join(supported_types)}"
                    )

                # 保存临时文件
                with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
                    content = await file.read()
                    temp_file.write(content)
                    temp_file_path = temp_file.name
                    temp_files.append(temp_file_path)

                # 提取文件内容
                file_content = milvus_insert._extract_text_from_file(temp_file_path, file_extension)

                files_data.append({
                    "filename": file.filename,
                    "content": file_content,
                    "file_type": file_extension
                })

            # 创建批量插入请求
            batch_request = BatchInsertRequest(
                rag_id=rag_id,
                split_strategy=split_strategy,
                update_mode=update_mode,
                auto_generate_rag_id=auto_generate_rag_id
            )

            # 批量插入文件
            result = milvus_insert.batch_insert_files(
                collection_name=milvus_insert.collection_name,
                files_data=files_data,
                batch_request=batch_request
            )

            if not result.success and result.failed_files == result.total_files:
                raise HTTPException(status_code=500, detail=result.message)

            return result

        finally:
            # 清理所有临时文件
            for temp_file_path in temp_files:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量文件上传API调用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量文件上传失败: {str(e)}")


@router.post("/text", response_model=InsertResponse)
async def insert_text_content(request: InsertRequest):
    """
    直接插入文本内容到知识库
    """
    try:
        if not request.content:
            raise HTTPException(status_code=400, detail="文本内容不能为空")

        result = milvus_insert.insert_file_content(
            collection_name=milvus_insert.collection_name,
            request=request
        )

        if not result.success:
            raise HTTPException(status_code=500, detail=result.message)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文本插入API调用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文本插入失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "insert",
        "timestamp": datetime.now().isoformat()
    }

