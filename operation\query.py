"""
查询知识库记录
用户输入query
"""

from fastapi import APIRouter,HTTPException
from pydantic import BaseModel
from typing import List, Optional
import json
import logging
try:
    # 尝试相对导入（当作为模块运行时）
    from .milvus import milvus_client
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import milvus_client

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/query", tags=["用户查询"])

class Query(BaseModel):
    # 用户问题
    query:str
    # 知识库的id
    rag_id:List[str]

class Response(BaseModel):
    # 回答
    answer: str
    # 文本块来源
    sources: List[dict]
    # 相关图片信息
    images: List[dict] = []


@router.post("/", response_model=Response)
async def query_endpoint(request: Query):
    try:
        # 1. milvus查询获取的内容
        content_list = milvus_client.query(request.query, request.rag_id, limit=5)
        
        if not content_list:
            raise HTTPException(status_code=404, detail="No relevant content found in Milvus")

        # 2. 整合内容
        combined_content = "\n\n".join([item["content"] for item in content_list])
        
        # 3. 构建提示词
        prompt = [
            {"role": "system", "content": "你是一位知识库问答专家"},
            {
                "role": "user",
                "content": f"请基于以下内容：\n{combined_content}\n请回答以下问题：{request.query}；尽量全面概括，不要回答除问题以外的内容"
            }
        ]

        # 4. Call LLM to generate answer
        response = milvus_client.client.chat.completions.create(
            model=milvus_client.llm_model_name,
            messages=prompt,
            max_tokens=500,  # Adjust as needed
            temperature=0.7  # Adjust as needed
        )
        answer = response.choices[0].message.content.strip()

        # 5. Format sources for response and extract images
        sources = []
        all_images = []

        for item in content_list:
            # 处理文本来源
            sources.append({
                "id": item["id"],
                "rag_id": item["rag_id"],
                "source": item["source"],
                "chunk_id": item["chunk_id"],
                "score": item["score"],
                "content": item["content"][:200],  # Truncate for brevity
                "has_images": item.get("has_images", False)
            })

            # 提取图片信息
            if item.get("has_images") and item.get("image_urls"):
                try:
                    image_data = json.loads(item["image_urls"]) if isinstance(item["image_urls"], str) else item["image_urls"]
                    if isinstance(image_data, list):
                        for img in image_data:
                            if isinstance(img, dict) and img.get("url"):
                                all_images.append({
                                    "id": img.get("id"),
                                    "url": img.get("url"),
                                    "filename": img.get("filename"),
                                    "page_number": img.get("page_number"),
                                    "slide_number": img.get("slide_number"),
                                    "alt_text": img.get("alt_text"),
                                    "caption": img.get("caption"),
                                    "source_chunk": item["chunk_id"]
                                })
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"解析图片信息失败: {str(e)}")

        # 6. Return structured response
        return Response(
            answer=answer,
            sources=sources,
            images=all_images
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")