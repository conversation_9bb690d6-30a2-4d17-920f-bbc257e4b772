/**
 * RAG图片链接解析器
 * 用于解析LLM回答中嵌入的图片链接，格式：描述:img[链接]
 * 
 * 创建时间：2025.06.23
 * 作者：RAG系统开发团队
 */

class RAGImageParser {
    constructor(options = {}) {
        this.options = {
            // 图片链接的正则表达式
            imagePattern: /([^:]+):img\[([^\]]+)\]/g,
            
            // 默认样式类名
            imageClass: 'rag-embedded-image',
            descriptionClass: 'rag-image-description',
            modalClass: 'rag-image-modal',
            
            // 是否自动创建模态框
            autoCreateModal: true,
            
            // 图片点击回调
            onImageClick: null,
            
            // 图片加载错误回调
            onImageError: null,
            
            // 自定义图片图标
            imageIcon: '📷',
            
            ...options
        };
        
        this.modalId = 'rag-image-modal-' + Date.now();
        
        if (this.options.autoCreateModal) {
            this.createModal();
        }
    }
    
    /**
     * 解析包含图片链接的文本
     * @param {string} text - 包含图片链接的原始文本
     * @param {Object} options - 解析选项
     * @returns {string} - 解析后的HTML
     */
    parse(text, options = {}) {
        const opts = { ...this.options, ...options };
        
        return text.replace(opts.imagePattern, (match, description, url) => {
            const cleanDescription = description.trim();
            const cleanUrl = url.trim();
            const imageId = 'img-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            return this.createImageElement(cleanDescription, cleanUrl, imageId, opts);
        });
    }
    
    /**
     * 创建图片元素HTML
     * @param {string} description - 图片描述
     * @param {string} url - 图片URL
     * @param {string} imageId - 图片ID
     * @param {Object} opts - 选项
     * @returns {string} - 图片元素HTML
     */
    createImageElement(description, url, imageId, opts) {
        const clickHandler = opts.onImageClick 
            ? `onclick="${opts.onImageClick}('${url}', '${description}', '${imageId}')"`
            : `onclick="window.ragImageParser.showImage('${url}', '${description}', '${imageId}')"`;
        
        return `<span class="${opts.imageClass}" 
                      data-url="${url}" 
                      data-description="${description}"
                      data-image-id="${imageId}"
                      ${clickHandler}
                      style="display: inline-block; margin: 2px 4px; padding: 4px 8px; 
                             background: #e3f2fd; border: 1px solid #2196f3; 
                             border-radius: 4px; cursor: pointer; 
                             transition: all 0.3s ease;"
                      onmouseover="this.style.background='#bbdefb'; this.style.transform='translateY(-1px)'"
                      onmouseout="this.style.background='#e3f2fd'; this.style.transform='translateY(0)'">
                    <span class="${opts.descriptionClass}" 
                          style="color: #1976d2; font-weight: bold; text-decoration: none;">
                        ${opts.imageIcon} ${description}
                    </span>
                </span>`;
    }
    
    /**
     * 显示图片模态框
     * @param {string} url - 图片URL
     * @param {string} description - 图片描述
     * @param {string} imageId - 图片ID
     */
    showImage(url, description, imageId) {
        const modal = document.getElementById(this.modalId);
        if (!modal) {
            console.error('图片模态框未找到');
            return;
        }
        
        const modalImage = modal.querySelector('.modal-image');
        const modalTitle = modal.querySelector('.modal-title');
        
        if (modalImage) {
            modalImage.src = url;
            modalImage.alt = description;
            
            // 添加加载错误处理
            modalImage.onerror = () => {
                if (this.options.onImageError) {
                    this.options.onImageError(url, description, imageId);
                } else {
                    modalImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                    modalImage.alt = '图片加载失败';
                }
            };
        }
        
        if (modalTitle) {
            modalTitle.textContent = description;
        }
        
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }
    
    /**
     * 关闭图片模态框
     */
    closeModal() {
        const modal = document.getElementById(this.modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = ''; // 恢复滚动
        }
    }
    
    /**
     * 创建图片模态框
     */
    createModal() {
        // 检查是否已存在
        if (document.getElementById(this.modalId)) {
            return;
        }
        
        const modalHTML = `
            <div id="${this.modalId}" class="${this.options.modalClass}" 
                 style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; 
                        width: 100%; height: 100%; background-color: rgba(0,0,0,0.8);">
                <div class="modal-content" 
                     style="position: relative; margin: 5% auto; max-width: 90%; max-height: 90%;">
                    <div class="modal-header" 
                         style="position: absolute; top: -50px; left: 0; right: 0; 
                                color: white; text-align: center;">
                        <h3 class="modal-title" style="margin: 0; font-size: 18px;"></h3>
                    </div>
                    <span class="close" 
                          style="position: absolute; top: -40px; right: 0; 
                                 color: white; font-size: 35px; font-weight: bold; 
                                 cursor: pointer; user-select: none;"
                          onclick="window.ragImageParser.closeModal()">&times;</span>
                    <img class="modal-image" 
                         style="width: 100%; height: auto; border-radius: 8px; 
                                max-height: 80vh; object-fit: contain;" 
                         src="" alt="">
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 绑定事件
        const modal = document.getElementById(this.modalId);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                this.closeModal();
            }
        });
    }
    
    /**
     * 提取文本中的所有图片信息
     * @param {string} text - 包含图片链接的文本
     * @returns {Array} - 图片信息数组
     */
    extractImages(text) {
        const images = [];
        let match;
        
        while ((match = this.options.imagePattern.exec(text)) !== null) {
            images.push({
                description: match[1].trim(),
                url: match[2].trim(),
                fullMatch: match[0]
            });
        }
        
        // 重置正则表达式的lastIndex
        this.options.imagePattern.lastIndex = 0;
        
        return images;
    }
    
    /**
     * 预加载图片
     * @param {Array} imageUrls - 图片URL数组
     * @returns {Promise} - 预加载Promise
     */
    preloadImages(imageUrls) {
        const promises = imageUrls.map(url => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(url);
                img.onerror = () => reject(url);
                img.src = url;
            });
        });
        
        return Promise.allSettled(promises);
    }
    
    /**
     * 销毁解析器
     */
    destroy() {
        const modal = document.getElementById(this.modalId);
        if (modal) {
            modal.remove();
        }
    }
}

// 创建全局实例
window.ragImageParser = new RAGImageParser();

// 导出模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RAGImageParser;
}

// AMD支持
if (typeof define === 'function' && define.amd) {
    define([], function() {
        return RAGImageParser;
    });
}

// 使用示例和文档
console.log('RAG图片解析器已加载');
console.log('使用方法：');
console.log('1. window.ragImageParser.parse(text) - 解析包含图片链接的文本');
console.log('2. window.ragImageParser.showImage(url, description) - 显示图片');
console.log('3. window.ragImageParser.extractImages(text) - 提取图片信息');
console.log('4. new RAGImageParser(options) - 创建自定义解析器实例');

/**
 * 使用示例：
 * 
 * // 基本使用
 * const parser = new RAGImageParser();
 * const htmlContent = parser.parse('这是组织架构图:img[http://example.com/image.png]');
 * 
 * // 自定义选项
 * const customParser = new RAGImageParser({
 *     imageIcon: '🖼️',
 *     onImageClick: (url, description, id) => {
 *         console.log('点击了图片:', description, url);
 *     },
 *     onImageError: (url, description, id) => {
 *         console.error('图片加载失败:', url);
 *     }
 * });
 * 
 * // 提取图片信息
 * const images = parser.extractImages(text);
 * console.log('找到的图片:', images);
 * 
 * // 预加载图片
 * const imageUrls = images.map(img => img.url);
 * parser.preloadImages(imageUrls).then(results => {
 *     console.log('图片预加载完成:', results);
 * });
 */
