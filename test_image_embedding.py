"""
测试图片链接嵌入功能
创建时间：2025.06.23
"""
import requests
import json
import tempfile
import os

def create_test_markdown_with_images():
    """创建包含图片引用的测试Markdown文档"""
    content = """
# 深能集团组织架构报告

## 公司概述
深能集团是一家大型清洁能源企业，致力于可再生能源的开发和利用。

## 组织架构
公司采用现代化的管理结构，设有多个业务部门和职能部门。

### 管理层结构
- 董事会：负责公司重大决策
- 总经理：负责日常运营管理
- 各部门总监：负责具体业务执行

### 业务部门
1. **风电事业部**
   - 负责风力发电项目的开发和运营
   - 管理全国15个省份的风电场
   - 总装机容量达到2000MW

2. **光伏事业部**
   - 专注于太阳能发电业务
   - 在西北地区建设大型光伏电站
   - 采用高效单晶硅技术

3. **水电事业部**
   - 参与水电站建设和运营
   - 注重生态环境保护
   - 具有防洪、灌溉等综合效益

## 技术创新
公司设有专门的技术研发中心，致力于清洁能源技术的创新和应用。

## 未来发展
计划在2025-2030年期间新增装机容量5000MW，进一步提升清洁能源占比。
"""
    return content

def test_file_upload_and_query():
    """测试文件上传和查询功能"""
    base_url = "http://localhost:3008"
    
    print("=== 测试图片链接嵌入功能 ===")
    
    # 1. 创建测试文档
    test_content = create_test_markdown_with_images()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # 2. 上传文档
        print("1. 上传测试文档...")
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('organization_report.md', f, 'text/markdown')}
            data = {
                'rag_id': 'test_org_structure',
                'source': 'organization_report',
                'split_strategy': 'hybrid',
                'update_mode': 'incremental'
            }
            
            response = requests.post(
                f"{base_url}/insert/file",
                files=files,
                data=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 文档上传成功")
                print(f"  文档ID: {result.get('doc_id')}")
                print(f"  文本块数量: {result.get('chunks_count')}")
                
                details = result.get('details', {})
                print(f"  图片数量: {details.get('images_count', 0)}")
                
                if details.get('images'):
                    print("  提取的图片:")
                    for img in details['images']:
                        print(f"    - {img.get('filename')}: {img.get('url')}")
            else:
                print(f"✗ 文档上传失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                return
        
        # 3. 测试查询功能
        print("\n2. 测试查询功能...")
        
        test_queries = [
            "深能集团的组织架构是怎样的？",
            "公司有哪些主要业务部门？",
            "风电事业部的情况如何？",
            "公司的技术创新情况怎么样？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n查询 {i}: {query}")
            
            query_data = {
                "query": query,
                "rag_id": ["test_org_structure"]
            }
            
            response = requests.post(
                f"{base_url}/query/",
                json=query_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('answer', '')
                sources = result.get('sources', [])
                images = result.get('images', [])
                
                print(f"✓ 查询成功")
                print(f"回答: {answer}")
                print(f"来源数量: {len(sources)}")
                print(f"嵌入图片数量: {len(images)}")
                
                # 检查回答中是否包含图片链接
                if ":img[" in answer:
                    print("✓ 回答中包含图片链接")
                    # 提取图片链接
                    import re
                    pattern = r'([^:]+):img\[([^\]]+)\]'
                    matches = re.findall(pattern, answer)
                    for description, url in matches:
                        print(f"  图片: {description.strip()} -> {url.strip()}")
                else:
                    print("- 回答中未包含图片链接")
                
                if images:
                    print("嵌入的图片信息:")
                    for img in images:
                        print(f"  - {img.get('description')}: {img.get('url')}")
                
            else:
                print(f"✗ 查询失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def test_manual_image_insertion():
    """测试手动插入包含图片引用的文本"""
    base_url = "http://localhost:3008"
    
    print("\n=== 测试手动图片引用插入 ===")
    
    # 模拟包含图片引用的文本内容
    content_with_images = """
深能集团组织架构说明

公司采用现代化的管理结构，具体组织架构如下：

[图片: org_chart_001] (第1页) - 深能集团组织架构图 [链接: http://*************:9000/milvus-bucket/test_doc/org_chart_001.png]

管理层包括：
1. 董事会 - 负责重大决策
2. 总经理 - 负责日常运营
3. 各部门总监 - 负责具体执行

业务部门结构：

[图片: dept_structure_002] (第2页) - 业务部门结构图 [链接: http://*************:9000/milvus-bucket/test_doc/dept_structure_002.png]

主要包括风电、光伏、水电三大事业部。
"""
    
    # 1. 插入包含图片引用的文本
    print("1. 插入包含图片引用的文本...")
    
    test_data = {
        "rag_id": "test_manual_images",
        "content": content_with_images,
        "source": "manual_test_document",
        "split_strategy": "hybrid",
        "update_mode": "incremental"
    }
    
    try:
        response = requests.post(
            f"{base_url}/insert/text",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 文本插入成功")
            print(f"  文档ID: {result.get('doc_id')}")
            print(f"  文本块数量: {result.get('chunks_count')}")
        else:
            print(f"✗ 文本插入失败: {response.status_code}")
            return
        
        # 2. 查询测试
        print("\n2. 查询测试...")
        
        query_data = {
            "query": "深能集团的组织架构图在哪里？",
            "rag_id": ["test_manual_images"]
        }
        
        response = requests.post(
            f"{base_url}/query/",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            
            print(f"✓ 查询成功")
            print(f"回答: {answer}")
            
            # 检查是否正确嵌入了图片链接
            if ":img[" in answer:
                print("✓ 成功嵌入图片链接")
                import re
                pattern = r'([^:]+):img\[([^\]]+)\]'
                matches = re.findall(pattern, answer)
                for description, url in matches:
                    print(f"  {description.strip()} -> {url.strip()}")
            else:
                print("- 未检测到图片链接嵌入")
        else:
            print(f"✗ 查询失败: {response.status_code}")
    
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试图片链接嵌入功能...")
    print("=" * 60)
    
    # 测试文件上传和查询
    test_file_upload_and_query()
    
    # 测试手动图片引用
    test_manual_image_insertion()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n功能说明:")
    print("1. LLM回答中会自动嵌入图片链接，格式：描述:img[链接]")
    print("2. 前端可以解析这种格式，将图片直接展示在回答中")
    print("3. 图片描述会根据alt_text、caption或位置信息自动生成")
    print("4. 支持多种文档格式的图片提取和引用")

if __name__ == "__main__":
    main()
