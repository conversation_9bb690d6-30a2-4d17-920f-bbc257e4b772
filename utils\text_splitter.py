"""
创建时间：2025.06.17
注释：通用文本切分——固定chunk,语义,混合（语义+固定）
"""
from typing import List, Optional, Dict, Tuple
import re
from langchain.text_splitter import RecursiveCharacterTextSplitter
import numpy as np
from dataclasses import dataclass
import logging
try:
    # 尝试相对导入（当作为模块运行时）
    from ..operation.milvus import milvus_client
    from .image_processor import ImageProcessor, ImageInfo
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import milvus_client
    from utils.image_processor import ImageProcessor, ImageInfo


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SplitConfig:
    """文本切分配置类"""
    chunk_size: int = 2048  # 固定chunk大小 - 增大以减少过细切分
    chunk_overlap: int = 512  # chunk重叠大小 - 相应增大重叠
    min_chunk_size: int = 200  # 最小chunk大小 - 提高最小大小
    semantic_threshold: float = 0.75  # 语义相似度阈值 - 降低阈值以增加语义块大小
    max_chunk_size: int = 4096  # 最大chunk大小 - 新增最大限制
    preserve_structure: bool = True  # 是否保持文档结构 - 新增结构保持选项

class TextSplitter:
    """文本切分器类，支持固定chunk、语义和混合切分策略，以及图片处理"""

    def __init__(self, config: Optional[SplitConfig] = None, enable_image_processing: bool = True):
        """
        初始化文本切分器

        Args:
            config: 切分配置，如果为None则使用默认配置
            enable_image_processing: 是否启用图片处理功能
        """
        self.config = config or SplitConfig()
        self.enable_image_processing = enable_image_processing
        self.recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap
        )
        try:
            self.semantic_model = milvus_client.embedding_model
        except Exception as e:
            logger.error(f"语义模型加载失败: {str(e)}")
            self.semantic_model = None

        # 初始化图片处理器
        if self.enable_image_processing:
            try:
                self.image_processor = ImageProcessor()
            except Exception as e:
                logger.error(f"图片处理器初始化失败: {str(e)}")
                self.image_processor = None
        else:
            self.image_processor = None

    def extract_images_from_file(self, file_path: str, file_type: str, doc_id: str) -> List[ImageInfo]:
        """
        从文件中提取图片

        Args:
            file_path: 文件路径
            file_type: 文件类型
            doc_id: 文档ID

        Returns:
            提取的图片信息列表
        """
        if not self.image_processor:
            return []

        return self.image_processor.extract_images_from_file(file_path, file_type, doc_id)

    def split_by_fixed_chunk(self, text: str, images: Optional[List[ImageInfo]] = None) -> List[str]:
        """
        使用固定大小进行文本切分

        Args:
            text: 待切分的文本
            images: 相关的图片信息列表

        Returns:
            切分后的文本块列表
        """
        try:
            chunks = self.recursive_splitter.split_text(text)
            filtered_chunks = [chunk for chunk in chunks if len(chunk) >= self.config.min_chunk_size]

            # 如果有图片信息，将图片引用添加到相关的文本块中
            if images and self.image_processor:
                filtered_chunks = self._embed_images_in_chunks(filtered_chunks, images)

            return filtered_chunks
        except Exception as e:
            logger.error(f"固定chunk切分失败: {str(e)}")
            return [text]

    def _embed_images_in_chunks(self, chunks: List[str], images: List[ImageInfo]) -> List[str]:
        """
        将图片引用嵌入到相关的文本块中

        Args:
            chunks: 文本块列表
            images: 图片信息列表

        Returns:
            嵌入图片引用后的文本块列表
        """
        if not images:
            return chunks

        enhanced_chunks = []
        for i, chunk in enumerate(chunks):
            enhanced_chunk = chunk

            # 为每个文本块添加相关的图片引用
            # 这里使用简单的策略：将图片平均分配到文本块中
            chunk_images = []
            if i < len(images):
                # 每个chunk最多关联一张图片，避免过度冗余
                chunk_images = [images[i]]

            # 将图片引用添加到文本块末尾
            for image in chunk_images:
                image_ref = self.image_processor.generate_image_reference(image)
                enhanced_chunk += f"\n\n{image_ref}"

            enhanced_chunks.append(enhanced_chunk)

        # 如果图片数量超过文本块数量，将剩余图片添加到最后一个块
        if len(images) > len(chunks) and enhanced_chunks:
            remaining_images = images[len(chunks):]
            for image in remaining_images:
                image_ref = self.image_processor.generate_image_reference(image)
                enhanced_chunks[-1] += f"\n\n{image_ref}"

        return enhanced_chunks

    def split_by_semantic(self, text: str, images: Optional[List[ImageInfo]] = None) -> List[str]:
        """
        使用语义相似度进行文本切分

        Args:
            text: 待切分的文本
            images: 相关的图片信息列表

        Returns:
            切分后的文本块列表
        """
        if not self.semantic_model:
            logger.warning("语义模型未加载，回退到固定chunk切分")
            return self.split_by_fixed_chunk(text, images)

        try:
            # 首先按句子分割
            sentences = re.split(r'[。！？.!?]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if len(sentences) <= 1:
                return [text]

            # 计算句子嵌入
            embeddings = self.semantic_model.encode(sentences)
            
            # 基于语义相似度合并句子
            chunks = []
            current_chunk = []
            current_embedding = None
            
            for i, (sentence, embedding) in enumerate(zip(sentences, embeddings)):
                if not current_chunk:
                    current_chunk.append(sentence)
                    current_embedding = embedding
                else:
                    # 计算当前句子与当前chunk的语义相似度
                    similarity = np.dot(embedding, current_embedding) / (
                        np.linalg.norm(embedding) * np.linalg.norm(current_embedding)
                    )
                    
                    if similarity >= self.config.semantic_threshold:
                        current_chunk.append(sentence)
                        # 更新当前chunk的嵌入（简单平均）
                        current_embedding = (current_embedding * len(current_chunk) + embedding) / (len(current_chunk) + 1)
                    else:
                        # 保存当前chunk并开始新的chunk
                        if current_chunk:
                            chunks.append(''.join(current_chunk))
                        current_chunk = [sentence]
                        current_embedding = embedding
            
            # 添加最后一个chunk
            if current_chunk:
                chunks.append(''.join(current_chunk))

            # 如果有图片信息，将图片引用添加到相关的文本块中
            if images and self.image_processor:
                chunks = self._embed_images_in_chunks(chunks, images)

            return chunks
        except Exception as e:
            logger.error(f"语义切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text, images)

    def split_by_hybrid(self, text: str, images: Optional[List[ImageInfo]] = None) -> List[str]:
        """
        使用混合策略进行文本切分（先语义切分，再固定chunk切分）

        Args:
            text: 待切分的文本
            images: 相关的图片信息列表

        Returns:
            切分后的文本块列表
        """
        try:
            # 首先进行语义切分（不传递图片，避免重复嵌入）
            semantic_chunks = self.split_by_semantic(text)

            # 对每个语义chunk进行固定大小切分
            final_chunks = []
            for chunk in semantic_chunks:
                if len(chunk) <= self.config.max_chunk_size:
                    final_chunks.append(chunk)
                else:
                    # 对超大的chunk进行进一步切分（不传递图片，避免重复嵌入）
                    fixed_chunks = self.split_by_fixed_chunk(chunk)
                    final_chunks.extend(fixed_chunks)

            # 在最终结果中嵌入图片引用
            if images and self.image_processor:
                final_chunks = self._embed_images_in_chunks(final_chunks, images)

            return final_chunks
        except Exception as e:
            logger.error(f"混合切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text, images)

    def split(self, text: str, strategy: str = "hybrid", images: Optional[List[ImageInfo]] = None) -> List[str]:
        """
        根据指定策略进行文本切分

        Args:
            text: 待切分的文本
            strategy: 切分策略，可选值：'fixed', 'semantic', 'hybrid'
            images: 相关的图片信息列表

        Returns:
            切分后的文本块列表
        """
        if not text:
            return []

        strategy = strategy.lower()
        #  使用固定大小进行文本切分
        if strategy == "fixed":
            return self.split_by_fixed_chunk(text, images)
        # 使用语义相似度进行文本切分
        elif strategy == "semantic":
            return self.split_by_semantic(text, images)
        # 使用混合策略进行文本切分
        elif strategy == "hybrid":
            return self.split_by_hybrid(text, images)
        else:
            logger.warning(f"未知的切分策略: {strategy}，使用默认的混合策略")
            return self.split_by_hybrid(text, images)

    def split_with_file_processing(self, text: str, file_path: str = None,
                                 file_type: str = None, doc_id: str = None,
                                 strategy: str = "hybrid") -> Tuple[List[str], List[ImageInfo]]:
        """
        处理文件并进行文本切分，同时提取图片

        Args:
            text: 待切分的文本
            file_path: 文件路径（用于图片提取）
            file_type: 文件类型
            doc_id: 文档ID
            strategy: 切分策略

        Returns:
            (切分后的文本块列表, 提取的图片信息列表)
        """
        images = []

        # 如果提供了文件信息，尝试提取图片
        if file_path and file_type and doc_id and self.enable_image_processing:
            try:
                images = self.extract_images_from_file(file_path, file_type, doc_id)
                logger.info(f"从文件 {file_path} 提取了 {len(images)} 张图片")
            except Exception as e:
                logger.error(f"图片提取失败: {str(e)}")

        # 进行文本切分
        chunks = self.split(text, strategy, images)

        return chunks, images
